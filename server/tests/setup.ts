import mongoose from 'mongoose';
import { MongoMemoryServer } from 'mongodb-memory-server';
// Global test setup
beforeAll(async () => {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.REFRESH_TOKEN_SECRET = 'test-refresh-secret';
  process.env.MONGO_TEST = "mongodb+srv://zypsierider:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0";
  
  // Increase timeout for database operations
  jest.setTimeout(30000);
});

// Global test cleanup
afterAll(async () => {
  // Close mongoose connections
  await mongoose.disconnect();
  
  // // Stop in-memory MongoDB instance
  // if (mongoServer) {
  //   await mongoServer.stop();
  // }
});

// Setup for each test file
beforeEach(async () => {
  // Clear all collections before each test
  if (mongoose.connection.readyState === 1) {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
  }
});

// Mock console methods to reduce noise in test output
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Mock external services
jest.mock('../config/cloudinary', () => ({
  uploader: {
    upload: jest.fn().mockResolvedValue({
      url: 'https://test-cloudinary-url.com/test-image.jpg',
      public_id: 'test-public-id',
    }),
    destroy: jest.fn().mockResolvedValue({ result: 'ok' }),
  },
}));

// Mock file upload utilities
jest.mock('../api/utilities/multer', () => ({
  upload: {
    single: jest.fn(() => (req: any, res: any, next: any) => next()),
    array: jest.fn(() => (req: any, res: any, next: any) => next()),
    fields: jest.fn(() => (req: any, res: any, next: any) => next()),
    any: jest.fn(() => (req: any, res: any, next: any) => next()),
  },
  processImage: jest.fn().mockResolvedValue({
    url: 'https://test-image-url.com/test.jpg',
    public_id: 'test-id',
  }),
  uploadToCloudinary: jest.fn().mockResolvedValue({
    url: 'https://test-cloudinary-url.com/test.jpg',
    public_id: 'test-id',
  }),
}));

// Helper function to create test database connection
export const createTestConnection = async () => {
  if (mongoose.connection.readyState === 0) {
    await mongoose.connect(process.env.MONGO_TEST || 'mongodb+srv://zypsierider:<EMAIL>/?retryWrites=true&w=majority&appName=Cluster0');
  }
  return mongoose.connection;
};

// Helper function to close test database connection
export const closeTestConnection = async () => {
  if (mongoose.connection.readyState !== 0) {
    await mongoose.connection.close();
  }
};

// Helper function to clear all test data
export const clearTestData = async () => {
  if (mongoose.connection.readyState === 1) {
    const collections = mongoose.connection.collections;
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
  }
};

// Setup and teardown functions for individual test files
export const setupTestEnvironment = async () => {
  await createTestConnection();
};

export const teardownTestEnvironment = async () => {
  await closeTestConnection();
};

export const clearDatabase = async () => {
  await clearTestData();
};

// Generate test utilities
const generateObjectId = () => {
  return new mongoose.Types.ObjectId();
};

const generateTestToken = (userId: string, userRole: string = 'admin') => {
  const jwt = require('jsonwebtoken');
  return jwt.sign(
    {
      userId,
      userRole,
      exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour
    },
    process.env.JWT_SECRET || 'test-jwt-secret'
  );
};

// Export test utilities
export const testUtils = {
  createTestConnection,
  closeTestConnection,
  clearTestData,
  generateObjectId,
  generateTestToken,
};
