import { Schema, Document, model } from "mongoose";
import constants from "../constants";
import { Base, BaseSchema } from "../types/common.types";

// TypeScript interface
export interface IItem extends Base {
  type: string;
  threshold: number;
  category: string;
  interval: string;
  initialStock: number;
  usedStock: number;
  remainingStock: number;
  ingredientType: "raw_material" | "semi_processed";
  processingRecipe?: string; // Reference to processing recipe if semi-processed
  parentIngredients?: string[]; // Raw materials used to create this semi-processed ingredient
  isProcessingLinked?: boolean; // Indicates if semi-processed ingredient has been linked to a processing recipe
  setupPhase?: "created" | "recipe_linked" | "complete"; // Track setup progress
  costPerUnit: number;
  unit: string;

  // Instance methods
  canBeUsedInProduction(): boolean;
  canBeUsedInOrder(): boolean;
  isSetupComplete(): boolean;
  needsProcessingRecipe(): boolean;
  isLowStock(): boolean;
  isOutOfStock(): boolean;
  getStockStatus(): string;
  stockPercentage: number;
}

export interface IItemCategory extends Base { }
export interface IItemType extends Base { }

export const ItemCategoryModel = model<IItemCategory>(
  constants.DB.ITEMCATEGORY,
  BaseSchema
);
export const ItemTypeModel = model<IItemType>(
  constants.DB.ITEMTYPE,
  BaseSchema
);
// Mongoose schema
const itemSchema = new Schema(
  {
    restaurant: {
      type: Schema.Types.ObjectId,
      ref: constants.DB.RESTAURANT,
      required: true,
    },
    name: {
      type: String,
      required: true,
      trim: true,
    },
    isActive: {
      type: Boolean,
      required: true,
      default: true,
    },
    type: {
      type: Schema.Types.ObjectId,
      ref: constants.DB.ITEMTYPE,
      required: true,
      trim: true,
    },
    threshold: {
      type: Number,
      min: 0,
    },
    interval: {
      type: String,
    },
    initialStock: {
      type: Number,
      default: 0,
      min: 0,
    },
    usedStock: {
      type: Number,
      default: 0,
      min: 0,
    },
    remainingStock: {
      type: Number,
      default: 0,
      min: 0,
    },
    category: {
      type: Schema.Types.ObjectId,
      ref: constants.DB.ITEMCATEGORY,
      required: true,
      trim: true,
    },
    ingredientType: {
      type: String,
      enum: Object.values(constants.INGREDIENTTYPE),
      default: constants.INGREDIENTTYPE.RAW_MATERIAL,
      required: true,
    },
    processingRecipe: {
      type: Schema.Types.ObjectId,
      ref: constants.DB.PROCESSINGRECIPE,
      // Note: Not required during creation to allow workflow where semi-processed
      // ingredient is created first, then processing recipe is created and linked back
      required: false,
    },
    parentIngredients: [{
      type: Schema.Types.ObjectId,
      ref: constants.DB.ITEM,
    }],
    isProcessingLinked: {
      type: Boolean,
      default: false,
      // Indicates if semi-processed ingredient has been properly linked to a processing recipe
    },
    setupPhase: {
      type: String,
      enum: ["created", "recipe_linked", "complete"],
      default: function () {
        return (this as any).ingredientType === constants.INGREDIENTTYPE.RAW_MATERIAL ? "complete" : "created";
      },
      // Track the setup progress for semi-processed ingredients
    },
    costPerUnit: {
      type: Number,
      default: 0,
      min: 0,
    },
    unit: {
      type: String,
      default: "units",
    },
  },
  {
    timestamps: true,
  }
);

itemSchema.index({ restaurant: 1, name: 1 }, { unique: true });
itemSchema.index({ category: 1 });
itemSchema.index({ status: 1 });
itemSchema.index({ ingredientType: 1 });
itemSchema.index({ processingRecipe: 1 });

// Pre-save validation hook
itemSchema.pre("save", function (next) {
  const item = this;

  // Validate stock levels
  if (item.remainingStock && item.remainingStock < 0) {
    return next(new Error(`Stock level for item ${item.name} cannot be negative`));
  }

  // Update setup phase and linking status for semi-processed ingredients
  if (item.ingredientType === constants.INGREDIENTTYPE.SEMI_PROCESSED) {
    if (item.processingRecipe && !item.isProcessingLinked) {
      item.isProcessingLinked = true;
      item.setupPhase = "recipe_linked";
    }

    // Mark as complete if it has processing recipe and parent ingredients
    if (item.processingRecipe && item.parentIngredients && item.parentIngredients.length > 0) {
      item.setupPhase = "complete";
    }
  }

  // Raw materials are always complete upon creation
  if (item.ingredientType === constants.INGREDIENTTYPE.RAW_MATERIAL) {
    item.setupPhase = "complete";
    item.isProcessingLinked = true; // Not applicable but set for consistency
  }

  next();
});

// Instance methods
itemSchema.methods.canBeUsedInProduction = function () {
  if (this.ingredientType === constants.INGREDIENTTYPE.RAW_MATERIAL) {
    return this.remainingStock > 0;
  }

  if (this.ingredientType === constants.INGREDIENTTYPE.SEMI_PROCESSED) {
    return this.remainingStock > 0 && this.setupPhase === "complete";
  }

  return false;
};

itemSchema.methods.canBeUsedInOrder = function () {
  return this.canBeUsedInProduction();
};

itemSchema.methods.isSetupComplete = function () {
  return this.setupPhase === "complete";
};

itemSchema.methods.needsProcessingRecipe = function () {
  return this.ingredientType === constants.INGREDIENTTYPE.SEMI_PROCESSED &&
    !this.processingRecipe;
};

// Static methods
itemSchema.statics.findIncompleteSetup = function (restaurantId) {
  return this.find({
    restaurant: restaurantId,
    ingredientType: constants.INGREDIENTTYPE.SEMI_PROCESSED,
    setupPhase: { $ne: "complete" }
  });
};

itemSchema.statics.findReadyForProduction = function (restaurantId) {
  return this.find({
    restaurant: restaurantId,
    ingredientType: constants.INGREDIENTTYPE.SEMI_PROCESSED,
    setupPhase: "complete",
    isActive: true
  });
};


// Pre-save hook to ensure stock fields are valid numbers
itemSchema.pre("save", function (next) {
  const item = this;

  // Ensure usedStock is always a valid number
  if (
    item.usedStock === undefined ||
    item.usedStock === null ||
    isNaN(item.usedStock)
  ) {
    item.usedStock = 0;
  }

  // Ensure remainingStock is always a valid number
  if (
    item.remainingStock === undefined ||
    item.remainingStock === null ||
    isNaN(item.remainingStock)
  ) {
    item.remainingStock = 0;
  }

  // Ensure initialStock is always a valid number
  if (
    item.initialStock === undefined ||
    item.initialStock === null ||
    isNaN(item.initialStock)
  ) {
    item.initialStock = 0;
  }

  // Validate that stock levels are not negative
  if (item.remainingStock < 0) {
    throw new Error(`Remaining stock for item ${item.name} cannot be negative`);
  }

  if (item.usedStock < 0) {
    throw new Error(`Used stock for item ${item.name} cannot be negative`);
  }

  next();
});
// Mongoose model
const Item = model<IItem>(constants.DB.ITEM, itemSchema);

export default Item;
